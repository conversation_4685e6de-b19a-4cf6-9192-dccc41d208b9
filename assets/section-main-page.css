.page-title {
  margin-top: 0;
}

.main-page-title {
  margin-bottom: 3rem;
}

@media screen and (min-width: 750px) {
  .main-page-title {
    margin-bottom: 4rem;
  }
}

.page-placeholder-wrapper {
  display: flex;
  justify-content: center;
}

.page-placeholder {
  width: 52.5rem;
  height: 52.5rem;
}

/* Dental Tour Page Specific Styles */
body.template-page.page-dental-tour #MainContent {
  background: linear-gradient(90deg, #8a5fe1, #8856af, #9560b0, #9f5ebb);
  min-height: 100vh;
}

body.template-page.page-dental-tour .main-page-title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .rte h1,
body.template-page.page-dental-tour .rte h2,
body.template-page.page-dental-tour .rte h3,
body.template-page.page-dental-tour .rte h4,
body.template-page.page-dental-tour .rte h5,
body.template-page.page-dental-tour .rte h6 {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .rte p {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-dental-tour .rte a {
  color: #ffffff;
  text-decoration: underline;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-dental-tour .rte a:hover {
  color: #f0f0f0;
}

body.template-page.page-dental-tour .rte strong {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .rte ul,
body.template-page.page-dental-tour .rte ol,
body.template-page.page-dental-tour .rte li {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
