.page-title {
  margin-top: 0;
}

.main-page-title {
  margin-bottom: 3rem;
}

@media screen and (min-width: 750px) {
  .main-page-title {
    margin-bottom: 4rem;
  }
}

.page-placeholder-wrapper {
  display: flex;
  justify-content: center;
}

.page-placeholder {
  width: 52.5rem;
  height: 52.5rem;
}

/* Dental Tour Page Specific Styles */
body.template-page.page-dental-tour {
  background: linear-gradient(90deg, #8a5fe1, #8856af, #9560b0, #9f5ebb) !important;
  min-height: 100vh;
  background-attachment: fixed;
}

/* Ensure the gradient covers the entire page */
body.template-page.page-dental-tour::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #8a5fe1, #8856af, #9560b0, #9f5ebb);
  z-index: -1;
  pointer-events: none;
}

body.template-page.page-dental-tour #MainContent {
  background: transparent;
}

body.template-page.page-dental-tour .page-width {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding: 3rem 2rem;
  box-shadow: 0 20px 40px rgba(138, 95, 225, 0.2);
  backdrop-filter: blur(10px);
}

body.template-page.page-dental-tour .main-page-title {
  color: #8a5fe1;
  text-shadow: 0 2px 4px rgba(138, 95, 225, 0.1);
  font-weight: 700;
}

body.template-page.page-dental-tour .rte {
  color: #4a5568;
}

body.template-page.page-dental-tour .rte h1,
body.template-page.page-dental-tour .rte h2,
body.template-page.page-dental-tour .rte h3,
body.template-page.page-dental-tour .rte h4,
body.template-page.page-dental-tour .rte h5,
body.template-page.page-dental-tour .rte h6 {
  color: #8a5fe1;
  font-weight: 600;
}

body.template-page.page-dental-tour .rte p {
  color: #2d3748;
  line-height: 1.7;
}

body.template-page.page-dental-tour .rte a {
  color: #9f5ebb;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

body.template-page.page-dental-tour .rte a:hover {
  color: #8a5fe1;
  text-decoration: underline;
}

body.template-page.page-dental-tour .rte strong {
  color: #8856af;
  font-weight: 700;
}

body.template-page.page-dental-tour .rte ul,
body.template-page.page-dental-tour .rte ol {
  color: #2d3748;
}

body.template-page.page-dental-tour .rte li {
  margin-bottom: 0.5rem;
}

/* Style other sections on the dental tour page */
body.template-page.page-dental-tour .section {
  background: rgba(255, 255, 255, 0.9);
  margin: 2rem auto;
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(138, 95, 225, 0.15);
  backdrop-filter: blur(5px);
}

body.template-page.page-dental-tour .contact-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(138, 95, 225, 0.2);
}

body.template-page.page-dental-tour .contact-form h2,
body.template-page.page-dental-tour .contact-form h3 {
  color: #8a5fe1 !important;
}

/* Style AI-generated blocks on dental tour page */
body.template-page.page-dental-tour [class*="ai-"] {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 2rem;
  margin: 2rem auto;
  box-shadow: 0 10px 30px rgba(138, 95, 225, 0.15);
}

/* Style video blocks */
body.template-page.page-dental-tour .ai-video-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 15px 35px rgba(138, 95, 225, 0.2);
}

/* Style before/after sliders */
body.template-page.page-dental-tour .ai-before-after-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 3rem 2rem;
  box-shadow: 0 15px 35px rgba(138, 95, 225, 0.2);
}

/* Style footer on dental tour page */
body.template-page.page-dental-tour .ai-footer {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 20px;
  margin: 2rem auto;
  box-shadow: 0 15px 35px rgba(138, 95, 225, 0.2);
}

body.template-page.page-dental-tour .ai-footer h3,
body.template-page.page-dental-tour .ai-footer h4 {
  color: #8a5fe1 !important;
}

body.template-page.page-dental-tour .ai-footer a {
  color: #9f5ebb !important;
}

body.template-page.page-dental-tour .ai-footer a:hover {
  color: #8a5fe1 !important;
}

/* Style any text content in blocks */
body.template-page.page-dental-tour [class*="ai-"] h1,
body.template-page.page-dental-tour [class*="ai-"] h2,
body.template-page.page-dental-tour [class*="ai-"] h3,
body.template-page.page-dental-tour [class*="ai-"] h4 {
  color: #8a5fe1 !important;
}

body.template-page.page-dental-tour [class*="ai-"] p,
body.template-page.page-dental-tour [class*="ai-"] span {
  color: #2d3748 !important;
}

body.template-page.page-dental-tour [class*="ai-"] a {
  color: #9f5ebb !important;
}

body.template-page.page-dental-tour [class*="ai-"] a:hover {
  color: #8a5fe1 !important;
}

@media screen and (max-width: 749px) {
  body.template-page.page-dental-tour .page-width {
    margin: 1rem;
    padding: 2rem 1.5rem;
    border-radius: 15px;
  }

  body.template-page.page-dental-tour .main-page-title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  body.template-page.page-dental-tour .section {
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 12px;
  }

  body.template-page.page-dental-tour .contact-form {
    padding: 2rem 1.5rem;
    border-radius: 15px;
  }
}
