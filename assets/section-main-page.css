.page-title {
  margin-top: 0;
}

.main-page-title {
  margin-bottom: 3rem;
}

@media screen and (min-width: 750px) {
  .main-page-title {
    margin-bottom: 4rem;
  }
}

.page-placeholder-wrapper {
  display: flex;
  justify-content: center;
}

.page-placeholder {
  width: 52.5rem;
  height: 52.5rem;
}

/* Dental Tour Page Specific Styles - Only MainContent */

body.template-page.page-dental-tour .main-page-title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .page-width .rte h1,
body.template-page.page-dental-tour .page-width .rte h2,
body.template-page.page-dental-tour .page-width .rte h3,
body.template-page.page-dental-tour .page-width .rte h4,
body.template-page.page-dental-tour .page-width .rte h5,
body.template-page.page-dental-tour .page-width .rte h6 {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .page-width .rte p {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-dental-tour .page-width .rte a {
  color: #ffffff;
  text-decoration: underline;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-dental-tour .page-width .rte a:hover {
  color: #f0f0f0;
}

body.template-page.page-dental-tour .page-width .rte strong {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .page-width .rte ul,
body.template-page.page-dental-tour .page-width .rte ol,
body.template-page.page-dental-tour .page-width .rte li {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Style all sections on dental-tour page */
body.template-page.page-dental-tour .section {
  background: transparent !important;
}

body.template-page.page-dental-tour .image-with-text {
  background: transparent !important;
}

body.template-page.page-dental-tour .image-with-text .image-with-text__content {
  background: transparent !important;
}

body.template-page.page-dental-tour .image-with-text h2,
body.template-page.page-dental-tour .image-with-text h3,
body.template-page.page-dental-tour .image-with-text h4 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour .image-with-text p {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-dental-tour .contact-form {
  background: transparent !important;
}

body.template-page.page-dental-tour .contact-form h2,
body.template-page.page-dental-tour .contact-form h3 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour [class*="ai-"] {
  background: transparent !important;
}

body.template-page.page-dental-tour [class*="ai-"] h1,
body.template-page.page-dental-tour [class*="ai-"] h2,
body.template-page.page-dental-tour [class*="ai-"] h3,
body.template-page.page-dental-tour [class*="ai-"] h4 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-dental-tour [class*="ai-"] p,
body.template-page.page-dental-tour [class*="ai-"] span {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Hair Surgery Page Specific Styles - Only MainContent */

body.template-page.page-hair-surgery .main-page-title {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery .page-width .rte h1,
body.template-page.page-hair-surgery .page-width .rte h2,
body.template-page.page-hair-surgery .page-width .rte h3,
body.template-page.page-hair-surgery .page-width .rte h4,
body.template-page.page-hair-surgery .page-width .rte h5,
body.template-page.page-hair-surgery .page-width .rte h6 {
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery .page-width .rte p {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-hair-surgery .page-width .rte a {
  color: #ffffff;
  text-decoration: underline;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-hair-surgery .page-width .rte a:hover {
  color: #f0f0f0;
}

body.template-page.page-hair-surgery .page-width .rte strong {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery .page-width .rte ul,
body.template-page.page-hair-surgery .page-width .rte ol,
body.template-page.page-hair-surgery .page-width .rte li {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Style all sections on hair-surgery page */
body.template-page.page-hair-surgery .section {
  background: transparent !important;
}

body.template-page.page-hair-surgery .image-with-text {
  background: transparent !important;
}

body.template-page.page-hair-surgery .image-with-text .image-with-text__content {
  background: transparent !important;
}

body.template-page.page-hair-surgery .image-with-text h2,
body.template-page.page-hair-surgery .image-with-text h3,
body.template-page.page-hair-surgery .image-with-text h4 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery .image-with-text p {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

body.template-page.page-hair-surgery .contact-form {
  background: transparent !important;
}

body.template-page.page-hair-surgery .contact-form h2,
body.template-page.page-hair-surgery .contact-form h3 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery [class*="ai-"] {
  background: transparent !important;
}

body.template-page.page-hair-surgery [class*="ai-"] h1,
body.template-page.page-hair-surgery [class*="ai-"] h2,
body.template-page.page-hair-surgery [class*="ai-"] h3,
body.template-page.page-hair-surgery [class*="ai-"] h4 {
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.template-page.page-hair-surgery [class*="ai-"] p,
body.template-page.page-hair-surgery [class*="ai-"] span {
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}
